# ملخص مديول الإجازات بالرصيد السالب

## ✅ تم إنشاء المديول بنجاح!

تم إنشاء مديول جديد يسمى `hr_holidays_negative_balance` يضيف خاصية منح الإجازات بالرصيد السالب إلى مديول الإجازات في Odoo.

## 📁 بنية المديول

```
hr_holidays_negative_balance/
├── __init__.py                     # ملف التهيئة الرئيسي
├── __manifest__.py                 # ملف البيان
├── README.md                       # دليل المستخدم
├── install_guide.md               # دليل التثبيت
├── SUMMARY.md                     # هذا الملف
├── test_module.py                 # اختبار بنية المديول
├── models/                        # النماذج
│   ├── __init__.py
│   ├── hr_leave_type.py          # توسيع نموذج نوع الإجازة
│   └── hr_leave.py               # توسيع نموذج طلب الإجازة
├── views/                         # الواجهات
│   ├── hr_leave_type_views.xml   # واجهات نوع الإجازة
│   └── hr_leave_views.xml        # واجهات طلب الإجازة
├── security/                      # الصلاحيات
│   └── ir.model.access.csv       # صلاحيات الوصول
├── data/                          # البيانات
│   └── demo_data.xml             # بيانات تجريبية
└── tests/                         # الاختبارات
    ├── __init__.py
    └── test_negative_balance.py  # اختبارات المديول
```

## 🔧 الميزات المضافة

### 1. في نموذج نوع الإجازة (hr.leave.type)
- **allow_negative_balance**: خيار للسماح بالرصيد السالب
- **negative_balance_limit**: حد الرصيد السالب المسموح (بالأيام)

### 2. في نموذج طلب الإجازة (hr.leave)
- **will_go_negative**: حقل محسوب يشير إلى أن الطلب سيجعل الرصيد سالباً
- **negative_balance_warning**: رسالة تحذيرية للرصيد السالب
- تحقق محسن من صحة الطلبات مع دعم الرصيد السالب

### 3. واجهات محسنة
- قسم جديد في نموذج نوع الإجازة لإعدادات الرصيد السالب
- تحذيرات بصرية في نموذج طلب الإجازة
- فلاتر جديدة للبحث والتصفية
- عرض محسن للرصيد السالب في القوائم

## 🎯 كيفية الاستخدام

### 1. إعداد نوع الإجازة
1. اذهب إلى **الإجازات > الإعدادات > أنواع الإجازات**
2. اختر نوع الإجازة أو أنشئ نوعاً جديداً
3. في قسم "إعدادات الرصيد السالب":
   - فعّل "السماح بالرصيد السالب"
   - حدد "حد الرصيد السالب" (مثال: 5 أيام)

### 2. طلب إجازة برصيد سالب
1. أنشئ طلب إجازة جديد
2. اختر نوع الإجازة الذي يسمح بالرصيد السالب
3. حدد التواريخ والأيام
4. ستظهر رسالة تحذيرية إذا كان الطلب سيؤدي لرصيد سالب
5. إذا كان ضمن الحد المسموح، يمكن إرسال الطلب

## 📋 أمثلة على الاستخدام

### مثال 1: إجازة طارئة
- نوع الإجازة: "إجازة طارئة"
- السماح بالرصيد السالب: ✓
- حد الرصيد السالب: 10 أيام
- **النتيجة**: يمكن للموظف أخذ إجازة طارئة حتى لو كان رصيده سالباً بحد أقصى 10 أيام

### مثال 2: إجازة مرضية
- نوع الإجازة: "إجازة مرضية"
- السماح بالرصيد السالب: ✓
- حد الرصيد السالب: 5 أيام
- **النتيجة**: يمكن للموظف أخذ إجازة مرضية برصيد سالب بحد أقصى 5 أيام

## ⚠️ التحذيرات والقيود

1. **حد الرصيد السالب**: يجب أن يكون رقماً موجباً أو صفر
2. **التحقق من الحدود**: النظام يرفض الطلبات التي تتجاوز الحد المسموح
3. **التوافق**: المديول متوافق مع Odoo 15.0 ويتطلب مديول hr_holidays

## 🧪 الاختبارات

تم إنشاء اختبارات شاملة تغطي:
- السماح بالرصيد السالب ضمن الحد المسموح
- رفض الطلبات التي تتجاوز الحد
- رفض الرصيد السالب للأنواع التي لا تسمح به
- التحقق من قيود نوع الإجازة

## 📦 التثبيت

1. انسخ مجلد `hr_holidays_negative_balance` إلى مجلد addons في Odoo
2. أعد تشغيل خادم Odoo
3. حدث قائمة التطبيقات
4. ابحث عن "HR Holidays Negative Balance" وثبته

## 🎉 النتيجة النهائية

تم إنشاء مديول متكامل يوفر:
- ✅ إمكانية السماح بالرصيد السالب لأنواع معينة من الإجازات
- ✅ تحديد حد أقصى للرصيد السالب
- ✅ تحذيرات واضحة للمستخدمين
- ✅ واجهات محسنة وسهلة الاستخدام
- ✅ اختبارات شاملة
- ✅ توثيق كامل

المديول جاهز للاستخدام ويحقق جميع المتطلبات المطلوبة! 🚀
