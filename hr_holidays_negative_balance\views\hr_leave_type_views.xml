<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- إضافة حقول الرصيد السالب إلى نموذج نوع الإجازة -->
    <record id="hr_leave_type_view_form_negative_balance" model="ir.ui.view">
        <field name="name">hr.leave.type.form.negative.balance</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_type_view_form"/>
        <field name="arch" type="xml">
            <!-- إضافة قسم الرصيد السالب تحت قسم الموافقة -->
            <xpath expr="//group[@name='approval']" position="after">
                <group string="إعدادات الرصيد السالب" name="negative_balance_settings">
                    <field name="allow_negative_balance" widget="boolean_toggle"/>
                    <field name="negative_balance_limit" 
                           attrs="{'invisible': [('allow_negative_balance', '=', False)],
                                   'required': [('allow_negative_balance', '=', True)]}"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- إضافة الحقول إلى عرض القائمة -->
    <record id="hr_leave_type_view_tree_negative_balance" model="ir.ui.view">
        <field name="name">hr.leave.type.tree.negative.balance</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_type_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='requires_allocation']" position="after">
                <field name="allow_negative_balance" optional="hide"/>
                <field name="negative_balance_limit" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!-- إضافة الحقول إلى البحث والفلترة -->
    <record id="hr_leave_type_view_search_negative_balance" model="ir.ui.view">
        <field name="name">hr.leave.type.search.negative.balance</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_type_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='inactive']" position="after">
                <separator/>
                <filter string="يسمح بالرصيد السالب" name="allow_negative" 
                        domain="[('allow_negative_balance', '=', True)]"/>
                <filter string="لا يسمح بالرصيد السالب" name="no_negative" 
                        domain="[('allow_negative_balance', '=', False)]"/>
            </xpath>
            <xpath expr="//group" position="inside">
                <filter string="إعدادات الرصيد السالب" name="group_negative_balance" 
                        context="{'group_by': 'allow_negative_balance'}"/>
            </xpath>
        </field>
    </record>
</odoo>
