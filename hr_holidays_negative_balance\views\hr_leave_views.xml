<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- إضافة معلومات الرصيد السالب إلى نموذج طلب الإجازة -->
    <record id="hr_leave_view_form_negative_balance" model="ir.ui.view">
        <field name="name">hr.leave.form.negative.balance</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form"/>
        <field name="arch" type="xml">
            <!-- إضافة الحقول المخفية للحسابات -->
            <xpath expr="//field[@name='holiday_allocation_id']" position="after">
                <field name="allow_negative_balance" invisible="1"/>
                <field name="negative_balance_limit" invisible="1"/>
                <field name="will_go_negative" invisible="1"/>
            </xpath>
            
            <!-- إضافة تحذير الرصيد السالب -->
            <xpath expr="//div[@name='duration_display']" position="after">
                <div class="alert alert-warning" role="alert" 
                     attrs="{'invisible': ['|', ('will_go_negative', '=', False), ('negative_balance_warning', '=', False)]}">
                    <i class="fa fa-exclamation-triangle"/> 
                    <field name="negative_balance_warning" readonly="1" nolabel="1"/>
                </div>
                
                <!-- معلومات إضافية عن الرصيد السالب -->
                <div class="alert alert-info" role="alert"
                     attrs="{'invisible': ['|', ('allow_negative_balance', '=', False), ('will_go_negative', '=', False)]}">
                    <i class="fa fa-info-circle"/>
                    <span> هذا النوع من الإجازات يسمح بالرصيد السالب حتى </span>
                    <field name="negative_balance_limit" readonly="1" nolabel="1"/>
                    <span> أيام.</span>
                </div>
            </xpath>
        </field>
    </record>

    <!-- إضافة الحقول إلى عرض القائمة -->
    <record id="hr_leave_view_tree_negative_balance" model="ir.ui.view">
        <field name="name">hr.leave.tree.negative.balance</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="before">
                <field name="will_go_negative" optional="hide" widget="boolean_toggle"/>
            </xpath>
        </field>
    </record>

    <!-- إضافة فلاتر للبحث -->
    <record id="hr_leave_view_search_negative_balance" model="ir.ui.view">
        <field name="name">hr.leave.search.negative.balance</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='approved']" position="after">
                <separator/>
                <filter string="سيصبح الرصيد سالباً" name="will_go_negative" 
                        domain="[('will_go_negative', '=', True)]"/>
                <filter string="يسمح بالرصيد السالب" name="allows_negative" 
                        domain="[('allow_negative_balance', '=', True)]"/>
            </xpath>
        </field>
    </record>

    <!-- تخصيص عرض الكانبان لإظهار تحذيرات الرصيد السالب -->
    <record id="hr_leave_view_kanban_negative_balance" model="ir.ui.view">
        <field name="name">hr.leave.kanban.negative.balance</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='holiday_status_id']" position="after">
                <field name="will_go_negative"/>
                <field name="allow_negative_balance"/>
                <field name="negative_balance_warning"/>
            </xpath>
            
            <xpath expr="//div[hasclass('oe_kanban_bottom_left')]" position="inside">
                <div t-if="record.will_go_negative.raw_value" class="text-warning">
                    <i class="fa fa-exclamation-triangle"/> رصيد سالب
                </div>
            </xpath>
        </field>
    </record>
</odoo>
