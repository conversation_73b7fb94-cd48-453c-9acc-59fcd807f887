<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- نوع إجازة تجريبي يسمح بالرصيد السالب -->
        <record id="holiday_status_negative_demo" model="hr.leave.type">
            <field name="name">إجازة طارئة (رصيد سالب)</field>
            <field name="requires_allocation">yes</field>
            <field name="employee_requests">yes</field>
            <field name="leave_validation_type">both</field>
            <field name="allow_negative_balance">True</field>
            <field name="negative_balance_limit">10.0</field>
            <field name="color_name">red</field>
            <field name="sequence">100</field>
        </record>

        <!-- نوع إجازة مرضية بحد سالب محدود -->
        <record id="holiday_status_sick_negative" model="hr.leave.type">
            <field name="name">إجازة مرضية (رصيد سالب محدود)</field>
            <field name="requires_allocation">yes</field>
            <field name="employee_requests">yes</field>
            <field name="leave_validation_type">hr</field>
            <field name="allow_negative_balance">True</field>
            <field name="negative_balance_limit">5.0</field>
            <field name="color_name">blue</field>
            <field name="sequence">110</field>
        </record>

        <!-- نوع إجازة عادي بدون رصيد سالب للمقارنة -->
        <record id="holiday_status_normal_demo" model="hr.leave.type">
            <field name="name">إجازة عادية (بدون رصيد سالب)</field>
            <field name="requires_allocation">yes</field>
            <field name="employee_requests">yes</field>
            <field name="leave_validation_type">hr</field>
            <field name="allow_negative_balance">False</field>
            <field name="negative_balance_limit">0.0</field>
            <field name="color_name">green</field>
            <field name="sequence">120</field>
        </record>
    </data>
</odoo>
