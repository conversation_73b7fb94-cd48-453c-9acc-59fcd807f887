# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from datetime import date, timedelta


class TestNegativeBalance(TransactionCase):

    def setUp(self):
        super().setUp()
        
        # إنشاء موظف تجريبي
        self.employee = self.env['hr.employee'].create({
            'name': 'موظف تجريبي',
            'user_id': self.env.user.id,
        })
        
        # إنشاء نوع إجازة يسمح بالرصيد السالب
        self.leave_type_negative = self.env['hr.leave.type'].create({
            'name': 'إجازة تجريبية سالبة',
            'requires_allocation': 'yes',
            'employee_requests': 'yes',
            'allow_negative_balance': True,
            'negative_balance_limit': 5.0,
        })
        
        # إنشاء نوع إجازة لا يسمح بالرصيد السالب
        self.leave_type_normal = self.env['hr.leave.type'].create({
            'name': 'إجازة تجريبية عادية',
            'requires_allocation': 'yes',
            'employee_requests': 'yes',
            'allow_negative_balance': False,
            'negative_balance_limit': 0.0,
        })
        
        # إنشاء تخصيص إجازة للموظف (3 أيام)
        self.allocation = self.env['hr.leave.allocation'].create({
            'name': 'تخصيص تجريبي',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_negative.id,
            'number_of_days': 3.0,
            'state': 'validate',
        })

    def test_negative_balance_allowed(self):
        """اختبار السماح بالرصيد السالب ضمن الحد المسموح"""
        # طلب إجازة 5 أيام (سيصبح الرصيد -2)
        leave = self.env['hr.leave'].create({
            'name': 'طلب إجازة تجريبي',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_negative.id,
            'date_from': date.today(),
            'date_to': date.today() + timedelta(days=4),
            'number_of_days': 5.0,
        })
        
        # يجب أن يكون الطلب صحيحاً
        self.assertTrue(leave.will_go_negative)
        self.assertTrue(leave.allow_negative_balance)
        
        # يجب أن يتم قبول الطلب بدون أخطاء
        leave.action_confirm()
        self.assertEqual(leave.state, 'confirm')

    def test_negative_balance_exceeds_limit(self):
        """اختبار رفض الطلب عند تجاوز حد الرصيد السالب"""
        # طلب إجازة 10 أيام (سيصبح الرصيد -7، يتجاوز الحد 5)
        with self.assertRaises(ValidationError):
            self.env['hr.leave'].create({
                'name': 'طلب إجازة يتجاوز الحد',
                'employee_id': self.employee.id,
                'holiday_status_id': self.leave_type_negative.id,
                'date_from': date.today(),
                'date_to': date.today() + timedelta(days=9),
                'number_of_days': 10.0,
            })

    def test_negative_balance_not_allowed(self):
        """اختبار رفض الرصيد السالب للأنواع التي لا تسمح به"""
        # إنشاء تخصيص للنوع العادي
        self.env['hr.leave.allocation'].create({
            'name': 'تخصيص عادي',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_normal.id,
            'number_of_days': 2.0,
            'state': 'validate',
        })
        
        # طلب إجازة 5 أيام (أكثر من المتاح)
        with self.assertRaises(ValidationError):
            self.env['hr.leave'].create({
                'name': 'طلب إجازة غير مسموح',
                'employee_id': self.employee.id,
                'holiday_status_id': self.leave_type_normal.id,
                'date_from': date.today(),
                'date_to': date.today() + timedelta(days=4),
                'number_of_days': 5.0,
            })

    def test_leave_type_constraints(self):
        """اختبار قيود نوع الإجازة"""
        # اختبار قيد الحد السالب
        with self.assertRaises(ValidationError):
            self.env['hr.leave.type'].create({
                'name': 'نوع خاطئ',
                'allow_negative_balance': True,
                'negative_balance_limit': -5.0,  # قيمة سالبة غير مسموحة
            })

    def test_onchange_allow_negative_balance(self):
        """اختبار تغيير خاصية السماح بالرصيد السالب"""
        leave_type = self.env['hr.leave.type'].create({
            'name': 'نوع للاختبار',
            'allow_negative_balance': True,
            'negative_balance_limit': 10.0,
        })
        
        # إلغاء السماح بالرصيد السالب
        leave_type.allow_negative_balance = False
        leave_type._onchange_allow_negative_balance()
        
        # يجب أن يصبح الحد صفر
        self.assertEqual(leave_type.negative_balance_limit, 0.0)
