# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrLeaveType(models.Model):
    _inherit = 'hr.leave.type'

    # خاصية السماح بالرصيد السالب
    allow_negative_balance = fields.Boolean(
        string='السماح بالرصيد السالب',
        default=False,
        help='السماح للموظفين بأخذ إجازات حتى لو لم يكن لديهم رصيد كافي'
    )
    
    # حد الرصيد السالب المسموح
    negative_balance_limit = fields.Float(
        string='حد الرصيد السالب (بالأيام)',
        default=0.0,
        help='عدد الأيام المسموح بها بالسالب. مثال: 5 يعني يمكن للموظف أن يكون رصيده -5 أيام'
    )

    @api.constrains('allow_negative_balance', 'negative_balance_limit')
    def _check_negative_balance_limit(self):
        """التحقق من صحة حد الرصيد السالب"""
        for record in self:
            if record.allow_negative_balance and record.negative_balance_limit < 0:
                raise ValidationError(_('حد الرصيد السالب يجب أن يكون رقماً موجباً أو صفر.'))
            if not record.allow_negative_balance and record.negative_balance_limit != 0:
                record.negative_balance_limit = 0.0

    @api.onchange('allow_negative_balance')
    def _onchange_allow_negative_balance(self):
        """عند إلغاء السماح بالرصيد السالب، إعادة تعيين الحد إلى صفر"""
        if not self.allow_negative_balance:
            self.negative_balance_limit = 0.0

    def get_employees_days(self, employee_ids, date=None):
        """
        توسيع دالة حساب أيام الموظفين لتشمل معلومات الرصيد السالب
        """
        result = super().get_employees_days(employee_ids, date)
        
        # إضافة معلومات الرصيد السالب لكل نوع إجازة
        for employee_id in employee_ids:
            for leave_type in self:
                if leave_type.id in result[employee_id]:
                    result[employee_id][leave_type.id].update({
                        'allow_negative_balance': leave_type.allow_negative_balance,
                        'negative_balance_limit': leave_type.negative_balance_limit,
                        'can_go_negative': leave_type.allow_negative_balance and leave_type.negative_balance_limit > 0,
                    })
        
        return result

    def name_get(self):
        """
        تخصيص عرض اسم نوع الإجازة لإظهار معلومات الرصيد السالب
        """
        if not self._context.get('employee_id'):
            return super().name_get()
        
        res = []
        for record in self:
            name = record.name
            if record.requires_allocation == "yes" and not self._context.get('from_manager_leave_form'):
                remaining = record.virtual_remaining_leaves
                max_leaves = record.max_leaves
                
                # إذا كان الرصيد سالباً وهذا النوع يسمح بالسالب
                if remaining < 0 and record.allow_negative_balance:
                    name = "%(name)s (%(count)s)" % {
                        'name': name,
                        'count': _('%(remaining)s متبقي من %(total)s (رصيد سالب)') % {
                            'remaining': remaining,
                            'total': max_leaves,
                        } + (_(' ساعات') if record.request_unit == 'hour' else _(' أيام'))
                    }
                else:
                    name = "%(name)s (%(count)s)" % {
                        'name': name,
                        'count': _('%(remaining)s متبقي من %(total)s') % {
                            'remaining': remaining or 0.0,
                            'total': max_leaves or 0.0,
                        } + (_(' ساعات') if record.request_unit == 'hour' else _(' أيام'))
                    }
            res.append((record.id, name))
        return res
