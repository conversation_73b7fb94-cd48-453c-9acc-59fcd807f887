# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from odoo.tools import float_compare


class HrLeave(models.Model):
    _inherit = 'hr.leave'

    # حقول محسوبة لعرض معلومات الرصيد السالب
    allow_negative_balance = fields.Boolean(
        related='holiday_status_id.allow_negative_balance',
        string='يسمح بالرصيد السالب',
        readonly=True
    )
    
    negative_balance_limit = fields.Float(
        related='holiday_status_id.negative_balance_limit',
        string='حد الرصيد السالب',
        readonly=True
    )
    
    will_go_negative = fields.Bo<PERSON>an(
        string='سيصبح الرصيد سالباً',
        compute='_compute_will_go_negative',
        help='يشير إلى أن هذا الطلب سيجعل رصيد الموظف سالباً'
    )
    
    negative_balance_warning = fields.Char(
        string='تحذير الرصيد السالب',
        compute='_compute_negative_balance_warning',
        help='رسالة تحذيرية حول الرصيد السالب'
    )

    @api.depends('holiday_status_id', 'number_of_days', 'employee_id')
    def _compute_will_go_negative(self):
        """حساب ما إذا كان الطلب سيجعل الرصيد سالباً"""
        for leave in self:
            leave.will_go_negative = False
            if (leave.holiday_status_id and leave.employee_id and 
                leave.holiday_status_id.requires_allocation == 'yes'):
                
                # الحصول على بيانات الرصيد الحالي
                leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])
                if leave.employee_id.id in leave_days and leave.holiday_status_id.id in leave_days[leave.employee_id.id]:
                    current_balance = leave_days[leave.employee_id.id][leave.holiday_status_id.id]['virtual_remaining_leaves']
                    new_balance = current_balance - leave.number_of_days
                    leave.will_go_negative = new_balance < 0

    @api.depends('will_go_negative', 'holiday_status_id', 'number_of_days', 'employee_id')
    def _compute_negative_balance_warning(self):
        """حساب رسالة التحذير للرصيد السالب"""
        for leave in self:
            leave.negative_balance_warning = ''
            if (leave.will_go_negative and leave.holiday_status_id and 
                leave.employee_id and leave.holiday_status_id.requires_allocation == 'yes'):
                
                leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])
                if leave.employee_id.id in leave_days and leave.holiday_status_id.id in leave_days[leave.employee_id.id]:
                    current_balance = leave_days[leave.employee_id.id][leave.holiday_status_id.id]['virtual_remaining_leaves']
                    new_balance = current_balance - leave.number_of_days
                    
                    if leave.holiday_status_id.allow_negative_balance:
                        if abs(new_balance) <= leave.holiday_status_id.negative_balance_limit:
                            leave.negative_balance_warning = _('تحذير: سيصبح رصيدك %(balance)s أيام') % {
                                'balance': new_balance
                            }
                        else:
                            leave.negative_balance_warning = _('خطأ: الرصيد السالب %(balance)s يتجاوز الحد المسموح %(limit)s أيام') % {
                                'balance': new_balance,
                                'limit': -leave.holiday_status_id.negative_balance_limit
                            }
                    else:
                        leave.negative_balance_warning = _('خطأ: الرصيد غير كافي. الرصيد الحالي: %(current)s، المطلوب: %(requested)s') % {
                            'current': current_balance,
                            'requested': leave.number_of_days
                        }

    @api.constrains('state', 'number_of_days', 'holiday_status_id')
    def _check_holidays(self):
        """
        تخصيص التحقق من صحة طلبات الإجازة لدعم الرصيد السالب
        """
        # الحصول على بيانات الرصيد لجميع الموظفين المعنيين
        mapped_days = self.holiday_status_id.get_employees_days((self.employee_id | self.sudo().employee_ids).ids)
        
        for holiday in self:
            # تخطي التحقق للحالات التي لا تحتاج تخصيص
            if (holiday.holiday_type != 'employee' or 
                not holiday.employee_id and not holiday.employee_ids or
                holiday.holiday_status_id.requires_allocation == 'no'):
                continue
            
            if holiday.employee_id:
                # التحقق للموظف الواحد
                leave_days = mapped_days[holiday.employee_id.id][holiday.holiday_status_id.id]
                remaining_leaves = leave_days['remaining_leaves']
                virtual_remaining_leaves = leave_days['virtual_remaining_leaves']
                
                # التحقق من الرصيد العادي أولاً
                if (float_compare(remaining_leaves, 0, precision_digits=2) >= 0 and
                    float_compare(virtual_remaining_leaves, 0, precision_digits=2) >= 0):
                    # الرصيد كافي، لا حاجة للتحقق من الرصيد السالب
                    continue
                
                # الرصيد غير كافي، التحقق من إمكانية الرصيد السالب
                if not holiday.holiday_status_id.allow_negative_balance:
                    # غير مسموح بالرصيد السالب
                    raise ValidationError(_(
                        'عدد أيام الإجازة المتبقية غير كافي لهذا النوع من الإجازات.\n'
                        'يرجى التحقق أيضاً من الإجازات المعلقة للموافقة.\n'
                        'الرصيد الحالي: %(current)s أيام، المطلوب: %(requested)s أيام'
                    ) % {
                        'current': virtual_remaining_leaves,
                        'requested': holiday.number_of_days
                    })
                
                # مسموح بالرصيد السالب، التحقق من الحد المسموح
                new_balance = virtual_remaining_leaves - holiday.number_of_days
                if abs(new_balance) > holiday.holiday_status_id.negative_balance_limit:
                    raise ValidationError(_(
                        'الرصيد السالب المطلوب (%(new_balance)s) يتجاوز الحد المسموح (%(limit)s أيام).\n'
                        'الرصيد الحالي: %(current)s أيام، المطلوب: %(requested)s أيام'
                    ) % {
                        'new_balance': new_balance,
                        'limit': -holiday.holiday_status_id.negative_balance_limit,
                        'current': virtual_remaining_leaves,
                        'requested': holiday.number_of_days
                    })
            
            else:
                # التحقق للموظفين المتعددين
                unallocated_employees = []
                negative_balance_employees = []
                
                for employee in holiday.employee_ids:
                    leave_days = mapped_days[employee.id][holiday.holiday_status_id.id]
                    remaining_leaves = leave_days['remaining_leaves']
                    virtual_remaining_leaves = leave_days['virtual_remaining_leaves']
                    
                    # التحقق من الرصيد العادي
                    if (float_compare(remaining_leaves, holiday.number_of_days, precision_digits=2) >= 0 and
                        float_compare(virtual_remaining_leaves, holiday.number_of_days, precision_digits=2) >= 0):
                        continue
                    
                    # الرصيد غير كافي
                    if not holiday.holiday_status_id.allow_negative_balance:
                        unallocated_employees.append(employee.name)
                        continue
                    
                    # التحقق من حد الرصيد السالب
                    new_balance = virtual_remaining_leaves - holiday.number_of_days
                    if abs(new_balance) > holiday.holiday_status_id.negative_balance_limit:
                        negative_balance_employees.append(employee.name)
                
                # رفع الأخطاء إذا وجدت
                if unallocated_employees:
                    raise ValidationError(_(
                        'عدد أيام الإجازة المتبقية غير كافي لهذا النوع من الإجازات.\n'
                        'يرجى التحقق أيضاً من الإجازات المعلقة للموافقة.\n'
                        'الموظفون الذين لا يملكون رصيداً كافياً:\n%s'
                    ) % (', '.join(unallocated_employees)))
                
                if negative_balance_employees:
                    raise ValidationError(_(
                        'الرصيد السالب المطلوب يتجاوز الحد المسموح لهذا النوع من الإجازات.\n'
                        'الموظفون المتأثرون:\n%s'
                    ) % (', '.join(negative_balance_employees)))
