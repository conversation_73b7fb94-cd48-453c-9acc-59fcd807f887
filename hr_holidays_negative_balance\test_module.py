#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لمديول الإجازات بالرصيد السالب
"""

def test_module_structure():
    """اختبار بنية المديول"""
    import os
    
    base_path = os.path.dirname(__file__)
    required_files = [
        '__init__.py',
        '__manifest__.py',
        'models/__init__.py',
        'models/hr_leave_type.py',
        'models/hr_leave.py',
        'views/hr_leave_type_views.xml',
        'views/hr_leave_views.xml',
        'security/ir.model.access.csv',
        'data/demo_data.xml',
        'tests/__init__.py',
        'tests/test_negative_balance.py',
    ]
    
    print("فحص بنية المديول...")
    missing_files = []
    
    for file_path in required_files:
        full_path = os.path.join(base_path, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {missing_files}")
        return False
    else:
        print("\n✅ جميع الملفات موجودة!")
        return True

def test_manifest():
    """اختبار ملف البيان"""
    import ast

    print("\nفحص ملف البيان...")

    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            manifest_content = f.read()

        # تحليل الملف كـ Python
        # البحث عن القاموس في الملف
        start_idx = manifest_content.find('{')
        if start_idx == -1:
            print("❌ لم يتم العثور على قاموس في ملف البيان")
            return False

        manifest_dict = ast.literal_eval(manifest_content[start_idx:])

        required_keys = ['name', 'version', 'depends', 'data']
        for key in required_keys:
            if key in manifest_dict:
                print(f"✓ {key}: {manifest_dict[key]}")
            else:
                print(f"❌ مفتاح مفقود: {key}")
                return False

        print("✅ ملف البيان صحيح!")
        return True

    except Exception as e:
        print(f"❌ خطأ في ملف البيان: {e}")
        return False

def test_xml_syntax():
    """اختبار صحة ملفات XML"""
    import xml.etree.ElementTree as ET
    import os
    
    print("\nفحص ملفات XML...")
    
    xml_files = [
        'views/hr_leave_type_views.xml',
        'views/hr_leave_views.xml',
        'data/demo_data.xml',
    ]
    
    for xml_file in xml_files:
        try:
            ET.parse(xml_file)
            print(f"✓ {xml_file}")
        except ET.ParseError as e:
            print(f"❌ خطأ في {xml_file}: {e}")
            return False
        except FileNotFoundError:
            print(f"❌ ملف غير موجود: {xml_file}")
            return False
    
    print("✅ جميع ملفات XML صحيحة!")
    return True

def test_python_syntax():
    """اختبار صحة ملفات Python"""
    import ast
    import os
    
    print("\nفحص ملفات Python...")
    
    python_files = [
        '__init__.py',
        'models/__init__.py',
        'models/hr_leave_type.py',
        'models/hr_leave.py',
        'tests/__init__.py',
        'tests/test_negative_balance.py',
    ]
    
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل الملف كـ Python
            ast.parse(content)
            print(f"✓ {py_file}")
            
        except SyntaxError as e:
            print(f"❌ خطأ نحوي في {py_file}: {e}")
            return False
        except FileNotFoundError:
            print(f"❌ ملف غير موجود: {py_file}")
            return False
    
    print("✅ جميع ملفات Python صحيحة!")
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("اختبار مديول الإجازات بالرصيد السالب")
    print("=" * 50)
    
    tests = [
        test_module_structure,
        test_manifest,
        test_xml_syntax,
        test_python_syntax,
    ]
    
    all_passed = True
    
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 جميع الاختبارات نجحت! المديول جاهز للتثبيت.")
    else:
        print("❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    print("=" * 50)

if __name__ == '__main__':
    main()
